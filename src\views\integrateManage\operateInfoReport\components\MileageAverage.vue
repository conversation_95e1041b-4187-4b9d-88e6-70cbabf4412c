<template>
  <div class="average-container">
    <SubTitle title="平均行驶里程统计"></SubTitle>
    <el-row>
      <el-col class="mt-10">
        <el-select v-model="typeValue" placeholder="请选择司机类型" class="w200">
          <el-option
            v-for="(item, index) in typeList"
            :key="index"
            :label="item"
            :value="index"
            clearable
            filterable
          ></el-option>
        </el-select>
      </el-col>
      <el-col class="mt-10">
        <TimeScreen></TimeScreen>
      </el-col>
    </el-row>
    <div class="chart-box">
      <div class="chart" id="average-chart"></div>
    </div>
  </div>
</template>

<script>
  import SubTitle from "./SubTitle.vue";
  import TimeScreen from "./TimeScreen.vue";
  import * as echarts from "echarts";
  export default {
    components: {
      SubTitle,
      TimeScreen,
    },
    data() {
      return {
        typeList: ["全体司机", "大型床位司机", "小型床位司机", "小诊所司机"],
        typeValue: 0,
      };
    },
    mounted() {},
    methods: {
      initChart() {},
    },
  };
</script>

<style lang="scss" scoped>
  .w200 {
    width: 200px;
  }
  .mt-12 {
    margin-top: 10px;
  }
  .average-container {
    background-color: #fff;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    padding: 16px;
    border-radius: 12px;
  }
  .chart-box {
    width: 100%;
    height: 300px;
    .chart {
      width: 100%;
      height: 100%;
    }
  }
</style>
